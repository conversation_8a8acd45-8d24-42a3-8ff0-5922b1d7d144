import pandas as pd
import math
import re
import logging  # 新增导入

# 导入日志配置
from logger_config import get_terminal_matching_logger, log_function_start, log_function_end, log_function_error, log_process_step, log_data_info, log_match_result

# 获取终端匹配日志记录器
logger = get_terminal_matching_logger()
logger.setLevel(logging.WARNING)  # 只显示WARNING及以上日志


def append_match_log(match_log, log_data):
    """
    辅助函数：向match_log追加新记录

    Args:
        match_log (pd.DataFrame): 现有的匹配日志DataFrame
        log_data (dict): 要追加的日志数据

    Returns:
        pd.DataFrame: 更新后的匹配日志DataFrame
    """
    new_log = pd.DataFrame([log_data])
    if not new_log.empty and not match_log.empty:
        return pd.concat([match_log, new_log], ignore_index=True)
    elif not new_log.empty:
        return new_log
    return match_log


def format_diameter(diam):
    if diam is None:
        return ""

    # 处理特殊字符串
    if isinstance(diam, str):
        diam_lower = diam.lower().strip()
        # 先检查完整的"地线/屏蔽层"字符串
        if "地线/屏蔽层" in diam_lower:
            return "地线/屏蔽层"
        # 再检查单独的"屏蔽层"或"地线"
        if "屏蔽层" in diam_lower or "地线" in diam_lower:
            return "屏蔽层"

    # 尝试转换为浮点数
    try:
        num = float(diam)
        # 统一保留1位小数
        return f"{num:.1f}"
    except (ValueError, TypeError):
        return str(diam).strip()


def create_interface_type_condition(sheet2, target_interface_type):
    """
    创建接口类型匹配条件，支持"长/短"的双向匹配

    参数:
    sheet2 -- 压头匹配表Sheet2的DataFrame
    target_interface_type -- 目标接口类型（如"长"、"短"等）

    返回:
    pandas Series: 布尔条件，用于筛选匹配的记录
    """
    if target_interface_type is None:
        # 如果目标接口类型为空，返回全True条件
        return pd.Series([True] * len(sheet2), index=sheet2.index)

    # 精确匹配目标接口类型
    exact_match = sheet2['接口类型'] == target_interface_type

    # 匹配包含"长/短"的记录（当目标是"长"或"短"时）
    if target_interface_type in ['长', '短']:
        flexible_match = sheet2['接口类型'] == '长/短'
        return exact_match | flexible_match
    else:
        return exact_match


def match_terminals(wire_count_df, parallel_df, terminal_match_file, printer_grouped_df=None, power_wire_df=None, multi_core_df=None):
    """
    压头匹配主函数 - 增加屏柜分组处理和多芯线支持
    """
    try:
        # logger.info("开始压头匹配处理（含并线功能和数据线压头），按屏柜分组处理")
        match_log = pd.DataFrame(columns=[
            '匹配类型', '匹配对象', '匹配条件', '匹配结果', '匹配数量', '备注'
        ])

        # 收集所有焊锡丝残值
        all_solder_residuals = []

        # 1. 读取压头匹配表
        # logger.info("正在读取压头匹配表...")
        sheet1 = pd.read_excel(terminal_match_file, sheet_name=0)  # Sheet1
        sheet2 = pd.read_excel(terminal_match_file, sheet_name=1)  # Sheet2
        sheet2['对应线径'] = sheet2['对应线径'].apply(format_diameter)

        # 获取所有PP接口类型的压头（数据线专用压头）
        pp_terminals = sheet2[sheet2['接口类型'] == 'PP']
        # logger.info(f"读取压头匹配表成功: Sheet1有{len(sheet1)}条记录, Sheet2有{len(sheet2)}条记录, 其中PP接口压头{len(pp_terminals)}种")

        # 2. 按屏柜分组处理
        all_terminal_counts = []  # 存储所有屏柜的压头结果

        # 获取屏柜列表（合并并线数据和导线统计数据的屏柜）
        cabinets_parallel = parallel_df['屏柜号'].unique().tolist() if parallel_df is not None and not parallel_df.empty and '屏柜号' in parallel_df.columns else []
        cabinets_wire = wire_count_df['屏柜编号'].unique().tolist() if wire_count_df is not None and not wire_count_df.empty and '屏柜编号' in wire_count_df.columns else []
        cabinets_power = power_wire_df['屏柜编号'].unique().tolist() if power_wire_df is not None and not power_wire_df.empty and '屏柜编号' in power_wire_df.columns else []
        cabinets_printer = printer_grouped_df['屏柜编号'].unique().tolist() if printer_grouped_df is not None and not printer_grouped_df.empty and '屏柜编号' in printer_grouped_df.columns else []
        cabinets_multi_core = multi_core_df['屏柜编号'].unique().tolist() if multi_core_df is not None and not multi_core_df.empty and '屏柜编号' in multi_core_df.columns else []
        all_cabinets = list(set(cabinets_parallel + cabinets_wire + cabinets_power + cabinets_printer + cabinets_multi_core))

        # logger.info(f"开始按屏柜处理压头匹配，共{len(all_cabinets)}个屏柜")

        # 只处理前3个屏柜的调试输出
        debug_cabinets = all_cabinets[:3]
        # print(f"调试模式：只输出前{len(debug_cabinets)}个屏柜的压头匹配数据")

        for i, cabinet in enumerate(all_cabinets):
            is_debug_cabinet = i < 3  # 只对前3个屏柜输出调试信息

            if is_debug_cabinet:
                # print(f"\n==== 开始处理屏柜: {cabinet} ====")
                pass
            cabinet_terminal_counts = []  # 当前屏柜的压头结果

            # 筛选当前屏柜的数据
            cabinet_parallel_df = parallel_df[parallel_df['屏柜号'] == cabinet] if parallel_df is not None and not parallel_df.empty and '屏柜号' in parallel_df.columns else pd.DataFrame()
            cabinet_wire_count_df = wire_count_df[wire_count_df['屏柜编号'] == cabinet] if wire_count_df is not None and not wire_count_df.empty and '屏柜编号' in wire_count_df.columns else pd.DataFrame()

            # 2.1 处理当前屏柜的并线压头匹配
            parallel_terminals, parallel_log = process_parallel_terminals(cabinet_parallel_df, sheet1, sheet2)
            if not parallel_log.empty:
                match_log = pd.concat([match_log, parallel_log], ignore_index=True)
            cabinet_terminal_counts.append(parallel_terminals)
            # if is_debug_cabinet and not parallel_terminals.empty:
            #     # print(f"屏柜{cabinet}并线压头匹配结果:")
            #     for _, terminal in parallel_terminals.iterrows():
            #         # print(f"  {terminal['压头名称']} (星瀚编码: {terminal['压头星瀚编码']}, 数量: {terminal['数量']})")
            #         pass

            # 2.2 处理当前屏柜的非并线端压头匹配
            non_parallel_terminals, non_parallel_log = process_non_parallel_terminals(cabinet_parallel_df, sheet1,
                                                                                      sheet2)
            if not non_parallel_log.empty:
                match_log = pd.concat([match_log, non_parallel_log], ignore_index=True)
            cabinet_terminal_counts.append(non_parallel_terminals)
            # if is_debug_cabinet and not non_parallel_terminals.empty:
            #     # print(f"屏柜{cabinet}非并线压头匹配结果:")
            #     for _, terminal in non_parallel_terminals.iterrows():
            #         # print(f"  {terminal['压头名称']} (星瀚编码: {terminal['压头星瀚编码']}, 数量: {terminal['数量']})")
            #         pass

            # 2.3 处理当前屏柜的导线统计表压头匹配
            wire_terminal_counts, wire_match_log = process_wire_terminal_matching(
                cabinet_wire_count_df,
                sheet1,
                sheet2
            )
            if not wire_match_log.empty:
                match_log = pd.concat([match_log, wire_match_log], ignore_index=True)
            cabinet_terminal_counts.append(wire_terminal_counts)
            # if is_debug_cabinet and not wire_terminal_counts.empty:
            #     # print(f"屏柜{cabinet}导线统计压头匹配结果:")
            #     for _, terminal in wire_terminal_counts.iterrows():
            #         # print(f"  {terminal['压头名称']} (星瀚编码: {terminal['压头星瀚编码']}, 数量: {terminal['数量']})")
            #         pass

            # === 新增：处理数据线压头 ===
            printer_terminals, printer_log, printer_residual_list = process_printer_terminals(
                cabinet,
                printer_grouped_df,
                pp_terminals,
                sheet1,
                sheet2,
                terminal_match_file
            )
            if not printer_log.empty:
                match_log = pd.concat([match_log, printer_log], ignore_index=True)
            cabinet_terminal_counts.append(printer_terminals)

            # 收集焊锡丝残值信息
            if printer_residual_list:
                all_solder_residuals.extend(printer_residual_list)
                logger.info(f"屏柜{cabinet}焊锡丝残值: {len(printer_residual_list)}项")
            # if is_debug_cabinet and not printer_terminals.empty:
            #     # print(f"屏柜{cabinet}数据线压头匹配结果:")
            #     for _, terminal in printer_terminals.iterrows():
            #         # print(f"  {terminal['压头名称']} (星瀚编码: {terminal['压头星瀚编码']}, 数量: {terminal['数量']})")
            #         pass

            # === 新增：处理电源线压头 ===
            # 过滤当前屏柜的电源线记录
            cabinet_power_wire = power_wire_df[
                power_wire_df['屏柜编号'] == cabinet] if power_wire_df is not None and not power_wire_df.empty and '屏柜编号' in power_wire_df.columns else pd.DataFrame()
            power_terminals, power_log = process_power_wire_terminals(cabinet_power_wire, sheet1, sheet2)
            if not power_log.empty:
                match_log = pd.concat([match_log, power_log], ignore_index=True)
            cabinet_terminal_counts.append(power_terminals)
            # if is_debug_cabinet and not power_terminals.empty:
            #     # print(f"屏柜{cabinet}电源线压头匹配结果:")
            #     for _, terminal in power_terminals.iterrows():
            #         # print(f"  {terminal['压头名称']} (星瀚编码: {terminal['压头星瀚编码']}, 数量: {terminal['数量']})")
            #         pass

            # === 新增：处理多芯线压头 ===
            # 过滤当前屏柜的多芯线记录
            cabinet_multi_core = multi_core_df[
                multi_core_df['屏柜编号'] == cabinet] if multi_core_df is not None and not multi_core_df.empty and '屏柜编号' in multi_core_df.columns else pd.DataFrame()
            multi_core_terminals, multi_core_log = process_multi_core_terminals(cabinet_multi_core, sheet1, sheet2)
            if not multi_core_log.empty:
                match_log = pd.concat([match_log, multi_core_log], ignore_index=True)
            cabinet_terminal_counts.append(multi_core_terminals)

            # 合并当前屏柜的所有压头结果
            cabinet_terminal_counts = pd.concat(cabinet_terminal_counts, ignore_index=True)
            # if is_debug_cabinet and not cabinet_terminal_counts.empty:
            #     # print(f"\n屏柜{cabinet}压头匹配汇总:")
            #     for _, terminal in cabinet_terminal_counts.iterrows():
            #         # print(f"  {terminal['压头名称']} (星瀚编码: {terminal['压头星瀚编码']}, 数量: {terminal['数量']})")
            #         pass

            # 为没有屏柜编号的记录设置屏柜编号（主要是并线数据）
            if not cabinet_terminal_counts.empty:
                cabinet_terminal_counts.loc[cabinet_terminal_counts['屏柜编号'] == '', '屏柜编号'] = cabinet
            all_terminal_counts.append(cabinet_terminal_counts)

        # 3. 合并所有屏柜的压头结果
        # logger.info("==== 开始合并所有屏柜的压头结果 ====")
        terminal_counts = pd.concat(all_terminal_counts, ignore_index=True)
        logger.info(f"合并所有屏柜压头结果: 总共{len(terminal_counts)}种压头")

        # 4. 新的合并逻辑：先按类型合并实际数量，再乘以概率
        if not terminal_counts.empty:
            # logger.info("正在按新逻辑合并压头数量...")

            # 步骤1：按屏柜编号、压头星瀚编码、压头星空编码、压头名称、概率分组，合并实际数量
            grouped_counts = terminal_counts.groupby(
                ['屏柜编号', '压头星瀚编码', '压头星空编码', '压头名称', '概率'], as_index=False
            ).agg({
                '实际数量': 'sum'  # 合并相同概率的实际数量
            })

            # 步骤2：对每组数据，将合并后的实际数量乘以概率
            merged_counts = []
            for idx, row in grouped_counts.iterrows():
                # 计算最终数量：实际数量 × 概率
                actual_quantity = row['实际数量']
                probability = row['概率']
                calculated_quantity = actual_quantity * probability

                # 在Sheet2中查找对应的损耗率
                terminal_match = sheet2[
                    (sheet2['压头星瀚编码'] == row['压头星瀚编码']) &
                    (sheet2['压头星空编码'] == row['压头星空编码']) &
                    (sheet2['压头名称'] == row['压头名称'])
                ]

                if not terminal_match.empty:
                    # 获取损耗率，如果为空则默认为0
                    loss_rate = terminal_match.iloc[0].get('损耗率', 0)
                    if pd.isna(loss_rate):
                        loss_rate = 0

                    # 确保损耗率是数值类型
                    try:
                        loss_rate = float(loss_rate)
                    except (ValueError, TypeError):
                        loss_rate = 0.0

                    # 应用损耗率：计算后数量 * (1 + 损耗率)
                    loss_adjusted_quantity = calculated_quantity * (1 + loss_rate)

                    # 焊锡丝不向上取整，保持原始数值（因为表示长度）
                    if '焊锡' in str(row['压头名称']):
                        final_quantity = loss_adjusted_quantity
                    else:
                        # 其他压头向上取整
                        final_quantity = math.ceil(loss_adjusted_quantity)

                    # 记录损耗率应用日志
                    match_log = append_match_log(match_log, {
                        '匹配类型': '新逻辑-损耗率应用',
                        '匹配对象': f"{row['压头名称']} ({row['压头星瀚编码']})",
                        '匹配条件': f"实际数量×概率×(1+损耗率): {actual_quantity}×{probability}×(1+{loss_rate})",
                        '匹配结果': '计算成功',
                        '匹配数量': final_quantity,
                        '备注': f"屏柜:{row['屏柜编号']}, 实际数量:{actual_quantity}, 概率:{probability}, 计算后:{calculated_quantity:.2f}, 损耗率:{loss_rate}, 调整后:{loss_adjusted_quantity:.2f}, 最终:{final_quantity}"
                    })
                else:
                    # 焊锡丝不向上取整，保持原始数值（因为表示长度）
                    if '焊锡' in str(row['压头名称']):
                        final_quantity = calculated_quantity
                    else:
                        # 其他压头向上取整
                        final_quantity = math.ceil(calculated_quantity)

                    # 记录未找到损耗率的日志
                    match_log = append_match_log(match_log, {
                        '匹配类型': '新逻辑-损耗率应用',
                        '匹配对象': f"{row['压头名称']} ({row['压头星瀚编码']})",
                        '匹配条件': f"实际数量×概率: {actual_quantity}×{probability}",
                        '匹配结果': '直接取整',
                        '匹配数量': final_quantity,
                        '备注': f"屏柜:{row['屏柜编号']}, 实际数量:{actual_quantity}, 概率:{probability}, 计算后:{calculated_quantity:.2f}, 最终:{final_quantity}"
                    })

                # 添加到最终结果
                merged_counts.append({
                    '屏柜编号': row['屏柜编号'],
                    '压头星瀚编码': row['压头星瀚编码'],
                    '压头星空编码': row['压头星空编码'],
                    '压头名称': row['压头名称'],
                    '数量': final_quantity
                })

            # 转换为DataFrame
            merged_counts = pd.DataFrame(merged_counts)

            # 步骤3：最终按屏柜编号和星瀚编码合并（用于输出表）
            if not merged_counts.empty:
                final_merged = merged_counts.groupby(
                    ['屏柜编号', '压头星瀚编码', '压头星空编码', '压头名称'], as_index=False
                )['数量'].sum()
                merged_counts = final_merged

            # 记录合并过程
            # logger.info(f"最终合并后剩余{len(merged_counts)}种压头")
            for _, row in merged_counts.iterrows():
                match_log = append_match_log(match_log, {
                    '匹配类型': '最终合并',
                    '匹配对象': f"{row['压头名称']} ({row['压头星瀚编码']})",
                    '匹配条件': '按屏柜和星瀚编码合并',
                    '匹配结果': '合并成功',
                    '匹配数量': row['数量'],
                    '备注': f"屏柜:{row['屏柜编号']}, 最终数量: {row['数量']}"
                })
        else:
            logger.warning("没有找到任何压头，无法合并")
            merged_counts = pd.DataFrame()
            match_log = append_match_log(match_log, {
                '匹配类型': '结果合并',
                '匹配对象': '所有压头',
                '匹配条件': '合并相同压头',
                '匹配结果': '无压头可合并',
                '匹配数量': 0,
                '备注': '没有找到任何压头'
            })

        logger.info(f"压头匹配处理完成，共匹配{len(merged_counts)}种压头，焊锡丝残值{len(all_solder_residuals)}项")
        return merged_counts, match_log, all_solder_residuals

    except Exception as e:
        logger.exception("压头匹配处理出错")
        raise


def process_parallel_terminals(parallel_df, sheet1, sheet2):
    """
    处理并线压头匹配

    参数:
    parallel_df -- 并线统计结果DataFrame
    sheet1 -- 压头匹配表Sheet1
    sheet2 -- 压头匹配表Sheet2

    返回:
    并线压头匹配结果DataFrame, 匹配日志DataFrame
    """
    terminal_counts = []
    match_log = pd.DataFrame(columns=[
        '匹配类型', '匹配对象', '匹配条件', '匹配结果', '匹配数量', '备注'
    ])

    logger.info(f"开始处理并线压头匹配: {len(parallel_df)}个并线组")

    # 检查DataFrame是否为空或缺少必要列
    if parallel_df.empty:
        logger.info("并线数据为空，跳过处理")
        return pd.DataFrame(), match_log

    required_columns = ['设备类型1', '设备类型2', '并线组号']
    missing_columns = [col for col in required_columns if col not in parallel_df.columns]
    if missing_columns:
        logger.warning(f"并线数据缺少必要列: {missing_columns}")
        return pd.DataFrame(), match_log

    # 步骤1: 处理屏蔽层/接地铜排的特殊情况
    logger.info("正在处理屏蔽层/接地铜排的特殊情况...")
    # print("🔍 [调试] 开始处理屏蔽层/接地铜排的特殊情况...")

    for idx, row in parallel_df.iterrows():
        dev_type1 = str(row['设备类型1'])
        dev_type2 = str(row['设备类型2'])

        # 新规则：检查设备类型1和设备类型2中是否同时包含"接地铜排"和"屏蔽层"
        combined_types = dev_type1 + dev_type2  # 合并两个设备类型字符串
        has_ground_bar = "接地铜排" in combined_types
        has_shield = "屏蔽层" in combined_types

        # 调试输出：显示每一行的检查结果
        # print(f"🔍 [调试] 行{idx}: 设备类型1='{dev_type1}', 设备类型2='{dev_type2}'")
        # print(f"🔍 [调试] 行{idx}: 合并字符串='{combined_types}'")
        # print(f"🔍 [调试] 行{idx}: 包含接地铜排={has_ground_bar}, 包含屏蔽层={has_shield}")

        if has_ground_bar and has_shield:
            logger.info(f"行{idx}: 同时包含接地铜排和屏蔽层，开始特殊匹配处理")

            # 步骤1: 匹配并接位置（端子排）的地线/屏蔽层压头
            ground_terminals = sheet2[sheet2['对应线径'] == '地线/屏蔽层']

            # 如果精确匹配失败，尝试匹配"屏蔽层"
            if ground_terminals.empty:
                ground_terminals = sheet2[sheet2['对应线径'] == '屏蔽层']

            if not ground_terminals.empty:
                terminal = ground_terminals.iloc[0]
                actual_count = 1  # 地线/屏蔽层实际数量为1
                terminal_counts.append({
                    '屏柜编号': '',  # 屏柜编号将在主函数中设置
                    '压头星瀚编码': terminal['压头星瀚编码'],
                    '压头星空编码': terminal['压头星空编码'],
                    '压头名称': terminal['压头名称'],
                    '实际数量': actual_count,  # 存储实际数量
                    '概率': 1.0               # 地线/屏蔽层概率为1
                })

                # 记录匹配日志
                match_log = append_match_log(match_log, {
                    '匹配类型': '并线压头',
                    '匹配对象': f"地线/屏蔽层并接位置 ({row['并线组号']})",
                    '匹配条件': f"设备类型1: {dev_type1}, 设备类型2: {dev_type2}",
                    '匹配结果': f"匹配到压头: {terminal['压头名称']} ({terminal['压头星瀚编码']})",
                    '匹配数量': actual_count,
                    '备注': '特殊规则：并接位置地线/屏蔽层压头'
                })
                logger.info(f"成功匹配并接位置压头: {terminal['压头名称']}, 实际数量: {actual_count}")
            else:
                match_log = append_match_log(match_log, {
                    '匹配类型': '并线压头',
                    '匹配对象': f"地线/屏蔽层并接位置 ({row['并线组号']})",
                    '匹配条件': f"设备类型1: {dev_type1}, 设备类型2: {dev_type2}",
                    '匹配结果': '匹配失败',
                    '匹配数量': 0,
                    '备注': '未找到地线/屏蔽层对应的压头'
                })
                logger.warning(f"未找到并接位置地线/屏蔽层对应的压头")

            # 步骤2: 为接地铜排的另一端匹配压头
            # 确定接地铜排端的信息
            if "接地铜排" in dev_type1:
                ground_wire_type = row['颜色/线径标识1']  # 通常是"花(**)"
                ground_wire_diameter = row['对应线径1']    # 通常是数字如"4"
                ground_device_type = "接地铜排"
            elif "接地铜排" in dev_type2:
                ground_wire_type = row['颜色/线径标识2']  # 通常是"花(**)"
                ground_wire_diameter = row['对应线径2']    # 通常是数字如"4"
                ground_device_type = "接地铜排"
            else:
                ground_wire_type = None
                ground_wire_diameter = None
                ground_device_type = None

            if ground_wire_type and ground_wire_diameter and ground_device_type:
                logger.info(f"为接地铜排另一端匹配压头: 线材={ground_wire_type}, 线径={ground_wire_diameter}")

                # 在Sheet1中查找接地铜排的接口类型
                ground_interface_matches = sheet1[sheet1['物料类别'] == ground_device_type]

                if not ground_interface_matches.empty:
                    for _, interface_row in ground_interface_matches.iterrows():
                        interface_type = interface_row['接口类型']
                        probability = interface_row['概率']

                        # 在Sheet2中匹配压头
                        formatted_diameter = format_diameter(ground_wire_diameter)

                        # 根据接口类型确定匹配条件
                        if interface_type == 'M6':
                            # M6接口类型，直接匹配
                            ground_terminal_conditions = [
                                sheet2['接口类型'] == 'M6',
                                sheet2['对应线径'] == formatted_diameter
                            ]
                        else:
                            # 其他接口类型，按并线要求匹配
                            if interface_type in ['单长', '单短', '双长', '双短']:
                                wire_type_code = interface_type[0]  # '单'或'双'
                                length_type = interface_type[1]     # '长'或'短'
                            else:
                                wire_type_code = '单'  # 默认单线
                                length_type = None

                            ground_terminal_conditions = [
                                sheet2['并线要求'] == wire_type_code,
                                sheet2['对应线径'] == formatted_diameter
                            ]

                            if length_type:
                                ground_terminal_conditions.append(create_interface_type_condition(sheet2, length_type))

                        # 合并条件
                        if ground_terminal_conditions:
                            combined_condition = ground_terminal_conditions[0]
                            for condition in ground_terminal_conditions[1:]:
                                combined_condition = combined_condition & condition
                            ground_matched_terminals = sheet2[combined_condition].copy()
                        else:
                            ground_matched_terminals = pd.DataFrame()

                        if not ground_matched_terminals.empty:
                            ground_terminal = ground_matched_terminals.iloc[0]
                            actual_ground_count = 1  # 接地铜排另一端实际数量为1

                            terminal_counts.append({
                                '屏柜编号': '',  # 屏柜编号将在主函数中设置
                                '压头星瀚编码': ground_terminal['压头星瀚编码'],
                                '压头星空编码': ground_terminal['压头星空编码'],
                                '压头名称': ground_terminal['压头名称'],
                                '实际数量': actual_ground_count,  # 存储实际数量
                                '概率': probability               # 存储概率
                            })

                            # 记录匹配日志
                            match_log = append_match_log(match_log, {
                                '匹配类型': '接地铜排压头',
                                '匹配对象': f"接地铜排另一端 ({row['并线组号']})",
                                '匹配条件': f"设备类型: {ground_device_type}, 线材: {ground_wire_type}, 线径: {ground_wire_diameter}, 接口: {interface_type}",
                                '匹配结果': f"匹配到压头: {ground_terminal['压头名称']} ({ground_terminal['压头星瀚编码']})",
                                '匹配数量': actual_ground_count,
                                '备注': f'特殊规则：接地铜排另一端压头，概率: {probability}'
                            })
                            logger.info(f"成功匹配接地铜排另一端压头: {ground_terminal['压头名称']}, 实际数量: {actual_ground_count}")
                            break  # 找到匹配后跳出循环
                        else:
                            match_log = append_match_log(match_log, {
                                '匹配类型': '接地铜排压头',
                                '匹配对象': f"接地铜排另一端 ({row['并线组号']})",
                                '匹配条件': f"设备类型: {ground_device_type}, 线材: {ground_wire_type}, 线径: {ground_wire_diameter}, 接口: {interface_type}",
                                '匹配结果': '匹配失败',
                                '匹配数量': 0,
                                '备注': '未找到匹配的接地铜排压头'
                            })
                else:
                    logger.warning(f"未找到接地铜排设备类型的接口匹配规则")
            else:
                logger.warning(f"无法确定接地铜排端的线材信息")

            # 步骤3: 屏蔽层端不需要压头（因为是从屏蔽线剥出来的）
            logger.info(f"屏蔽层端不需要压头（从屏蔽线剥出）")

            # 注意：跳过后续的正常并线匹配逻辑，因为已经完成了特殊处理
            continue
        else:
            # print(f"⏭️ [调试] 行{idx}: 不满足条件，跳过特殊处理")
            pass

    # 步骤2: 分组处理同一设备所属的并线
    logger.info("正在分组处理并线组...")
    parallel_df = parallel_df.copy()  # 创建副本避免SettingWithCopyWarning
    parallel_df['公共组号'] = parallel_df['并线组号'].apply(
        lambda x: x.split('_')[-1].split(':')[0] if '_' in x and ':' in x else None
    )
    parallel_df = parallel_df[parallel_df['公共组号'].notnull()]
    grouped = parallel_df.groupby('公共组号')

    logger.info(f"分组处理并线组: {len(grouped)}个公共组")

    for group_key, group in grouped:
        group_log = []

        # 步骤3: 获取point数量（该组中的并线数量）
        point_count = len(group)
        group_log.append(f"并线组: {group_key}, 点数: {point_count}")

        # 步骤4: 确定设备类型
        start_point = group.iloc[0]['导线1起点']
        if start_point and group_key in start_point:
            parts = start_point.split('/')
            if parts[0] == group_key:
                device_type = group.iloc[0]['设备类型1'].split('/')[0]
                group_log.append(f"起点设备类型: {device_type} (左侧)")
            elif len(parts) > 1 and parts[1] == group_key:
                device_type = group.iloc[0]['设备类型1'].split('/')[-1]
                group_log.append(f"终点设备类型: {device_type} (右侧)")
            else:
                device_type = group.iloc[0]['设备类型1'].split('/')[0] if '/' in group.iloc[0]['设备类型1'] else \
                group.iloc[0]['设备类型1']
                group_log.append(f"默认设备类型: {device_type}")
        else:
            device_type = group.iloc[0]['设备类型1'].split('/')[0] if '/' in group.iloc[0]['设备类型1'] else \
            group.iloc[0]['设备类型1']
            group_log.append(f"默认设备类型: {device_type}")

        # 步骤5: 准备匹配参数
        wire_type = group.iloc[0]['颜色/线径标识1']
        wire_diameter = group.iloc[0]['对应线径1']
        group_log.append(f"线材: {wire_type}, 线径: {wire_diameter}")

        # 步骤6: 在Sheet1中匹配接口类型
        interface_match = None
        for _, sheet1_row in sheet1.iterrows():
            if sheet1_row['物料类别'] != device_type:
                continue

            wire_property = str(sheet1_row.get('线材属性', ''))
            group_log.append(f"检查设备类型匹配: {device_type} 线材属性: {wire_property}")

            if not wire_property or wire_property.strip() == '':
                interface_match = sheet1_row
                group_log.append(f"匹配成功: 空线材属性")
                break

            if evaluate_wire_property(wire_type, point_count, wire_property):
                interface_match = sheet1_row
                group_log.append(f"匹配成功: 线材属性条件满足")
                break
            else:
                group_log.append(f"跳过: 线材属性条件不满足")

        if interface_match is None:
            log_msg = f"未找到匹配的接口类型: 设备类型={device_type}, 线材={wire_type}, point={point_count}"
            logger.warning(log_msg)
            group_log.append(log_msg)

            # 记录匹配失败日志
            match_log = append_match_log(match_log, {
                '匹配类型': '并线接口',
                '匹配对象': f"{group_key} (并线组)",
                '匹配条件': f"设备类型: {device_type}, 线材: {wire_type}, 点数: {point_count}",
                '匹配结果': '匹配失败',
                '匹配数量': 0,
                '备注': '未找到接口类型匹配'
            })
            continue
        else:
            interface_type = interface_match['接口类型']
            probability = interface_match['概率']
            group_log.append(f"匹配到接口类型: {interface_type}, 概率: {probability}")

        # 步骤7: 在Sheet2中匹配压头
        if isinstance(interface_type, str):
            if interface_type in ['单长', '单短', '双长', '双短']:
                length_type = interface_type[1]  # '长'或'短'
                wire_type_code = interface_type[0]  # '单'或'双'
            elif interface_type in ['单', '双']:
                wire_type_code = interface_type
                length_type = None
            else:
                wire_type_code = '单'
                length_type = None
        else:
            wire_type_code = '单'
            length_type = None

        formatted_diam = format_diameter(wire_diameter)
        terminal_conditions = [
            sheet2['并线要求'] == wire_type_code,
        ]
        terminal_conditions.append(sheet2['对应线径'] == formatted_diam)

        if length_type:
            terminal_conditions.append(create_interface_type_condition(sheet2, length_type))

        # 将所有条件合并为一个条件，避免索引不匹配警告
        if terminal_conditions:
            combined_condition = terminal_conditions[0]
            for condition in terminal_conditions[1:]:
                combined_condition = combined_condition & condition
            terminal_match = sheet2[combined_condition].copy()
        else:
            terminal_match = sheet2.copy()

        group_log.append(f"压头匹配条件: 并线要求={wire_type_code}, 线径={wire_diameter}, 接口类型={length_type}")

        if not terminal_match.empty:
            terminal = terminal_match.iloc[0]

            # 根据线材类型计算压头数量
            if wire_type == '四芯线':
                # 四芯线：每根导线需要4个压头
                actual_wire_count = point_count * 4
            elif wire_type == '两芯线':
                # 两芯线：每根导线需要2个压头
                actual_wire_count = point_count * 2
            else:
                # 其他线材：按原有逻辑处理
                actual_wire_count = point_count

            # 修改：存储实际数量，不乘以概率
            actual_count = actual_wire_count  # 实际匹配数量

            terminal_counts.append({
                '屏柜编号': '',  # 并线数据的屏柜编号将在主函数中设置
                '压头星瀚编码': terminal['压头星瀚编码'],
                '压头星空编码': terminal['压头星空编码'],
                '压头名称': terminal['压头名称'],
                '实际数量': actual_count,  # 存储实际数量
                '概率': probability        # 存储概率，稍后使用
            })

            # 记录匹配成功日志
            match_log = append_match_log(match_log, {
                '匹配类型': '并线压头',
                '匹配对象': f"{group_key} (并线组)",
                '匹配条件': f"设备类型: {device_type}, 线材: {wire_type}, 点数: {point_count}, 接口: {interface_type}",
                '匹配结果': f"匹配到压头: {terminal['压头名称']} ({terminal['压头星瀚编码']})",
                '匹配数量': actual_count,
                '备注': f"实际数量: {actual_count}, 概率: {probability}"
            })

            group_log.append(f"匹配到压头: {terminal['压头名称']}, 实际数量: {actual_count}, 概率: {probability}")
        else:
            log_msg = f"未找到匹配的压头: 并线要求={wire_type_code}, 线径={wire_diameter}, 接口类型={length_type}"
            logger.warning(log_msg)
            group_log.append(log_msg)

            # 记录匹配失败日志
            match_log = append_match_log(match_log, {
                '匹配类型': '并线压头',
                '匹配对象': f"{group_key} (并线组)",
                '匹配条件': f"并线要求: {wire_type_code}, 线径: {wire_diameter}, 接口类型: {length_type}",
                '匹配结果': '匹配失败',
                '匹配数量': 0,
                '备注': '未找到压头匹配'
            })

        # 记录详细组日志
        logger.debug(f"并线组 {group_key} 处理详情:\n" + "\n".join(group_log))

    return (pd.DataFrame(terminal_counts) if terminal_counts else pd.DataFrame(),
            match_log)


def evaluate_wire_property(wire_color, point_count, wire_property):
    """
    评估线材属性条件是否满足 - 支持U/I的"或"逻辑

    参数:
    wire_color -- 实际线材颜色/标识（如"U1"）
    point_count -- 并线点数
    wire_property -- 匹配条件表达式（如"U/I"表示包含U或I）

    返回:
    bool: 是否满足条件
    """
    # 空条件直接通过
    if wire_property is None or isinstance(wire_property, float):
        return True

    # 确保是字符串类型
    wire_property = str(wire_property)

    # 空条件直接通过
    if not wire_property or wire_property.strip() == '':
        return True

    # 预处理：移除空格并分割条件
    conditions = wire_property.replace(" ", "").split('&')
    valid_conditions = []

    # 检查U/I存在性条件
    has_u = 'U' in wire_color
    has_i = 'I' in wire_color

    for cond in conditions:
        # 处理点数量条件
        if cond.startswith('point'):
            operator = re.findall(r'([<>=!]=?)', cond)[0]
            value = int(re.findall(r'\d+', cond)[0])
            actual_value = point_count

            # 执行数值比较
            if operator == '<=' and actual_value > value:
                return False
            elif operator == '>=' and actual_value < value:
                return False
            elif operator == '==' and actual_value != value:
                return False
            elif operator == '!=' and actual_value == value:
                return False
            elif operator == '<' and actual_value >= value:
                return False
            elif operator == '>' and actual_value <= value:
                return False

        # 处理U/I条件（支持"或"逻辑）
        elif cond == 'U/I':  # 包含U或包含I
            if not (has_u or has_i):
                return False
        elif cond == '~U/I':  # 既不包含U也不包含I
            if has_u or has_i:
                return False
        # 处理单个符号条件（保持原逻辑）
        elif cond == '~U' and has_u:
            return False
        elif cond == 'U' and not has_u:
            return False
        elif cond == '~I' and has_i:
            return False
        elif cond == 'I' and not has_i:
            return False

    return True


def process_non_parallel_terminals(parallel_df, sheet1, sheet2):
    """
    处理非并线端压头匹配 - 修改：仅使用从并线数据中提取的非并线端
    """
    match_log = pd.DataFrame(columns=[
        '匹配类型', '匹配对象', '匹配条件', '匹配结果', '匹配数量', '备注'
    ])

    logger.info("开始处理非并线端压头匹配（独立数据源）")

    # 仅从并线数据中提取非并线端
    logger.info("正在从并线数据中提取非并线端设备...")
    non_parallel_data = []
    for _, row in parallel_df.iterrows():
        # 提取设备类型1的非并线端（起点）
        dev_type1 = str(row['设备类型1'])
        if '/' in dev_type1:
            start_dev_type = dev_type1.split('/')[0]
            non_parallel_data.append({
                '设备类型': start_dev_type,
                '颜色/线径标识': row['颜色/线径标识1'],
                '对应线径': row['对应线径1'],
                '设备类型（起点/终点）': f"{start_dev_type}/并线端"
            })

        # 提取设备类型2的非并线端（终点）
        dev_type2 = str(row['设备类型2'])
        if '/' in dev_type2:
            end_dev_type = dev_type2.split('/')[-1]
            non_parallel_data.append({
                '设备类型': end_dev_type,
                '颜色/线径标识': row['颜色/线径标识2'],
                '对应线径': row['对应线径2'],
                '设备类型（起点/终点）': f"并线端/{end_dev_type}"
            })

    # 创建非并线端DataFrame（独立数据源）
    non_parallel_df = pd.DataFrame(non_parallel_data) if non_parallel_data else pd.DataFrame()
    logger.info(f"提取的非并线端数据量: {len(non_parallel_df)}")

    # 使用独立数据源处理非并线端
    if not non_parallel_df.empty:
        logger.info("开始匹配非并线端接口类型...")
        # 匹配接口类型
        processed_wires, interface_log = match_interface_type(non_parallel_df, sheet1)
        if not interface_log.empty:
            if not match_log.empty:
                match_log = pd.concat([match_log, interface_log], ignore_index=True)
            else:
                match_log = interface_log

        logger.info("开始匹配非并线端压头记录...")
        # 匹配压头记录
        terminal_counts, terminal_log = match_terminal_records(processed_wires, sheet2)
        if not terminal_log.empty:
            if not match_log.empty:
                match_log = pd.concat([match_log, terminal_log], ignore_index=True)
            else:
                match_log = terminal_log

        logger.info(f"完成非并线端压头匹配: 找到{len(terminal_counts)}种压头")
        return terminal_counts, match_log

    else:
        logger.info("没有非并线端数据可处理")
        match_log = append_match_log(match_log, {
            '匹配类型': '非并线压头',
            '匹配对象': '所有设备',
            '匹配条件': '非并线端',
            '匹配结果': '无数据可处理',
            '匹配数量': 0,
            '备注': '没有非并线端数据'
        })
        return pd.DataFrame(), match_log


def match_interface_type(df, sheet1):
    """匹配接口类型和概率（使用新条件评估函数）"""
    result = []
    match_log = pd.DataFrame(columns=[
        '匹配类型', '匹配对象', '匹配条件', '匹配结果', '匹配数量', '备注'
    ])

    logger.info(f"开始接口类型匹配: {len(df)}条记录")

    if df.empty:
        logger.warning("接口类型匹配数据为空，跳过处理")
        return pd.DataFrame(), match_log

    for idx, row in df.iterrows():
        row_log = []
        row_log.append(f"处理记录 {idx}: 设备类型={row['设备类型']}, 线材={row['颜色/线径标识']}")

        # 获取该设备类型的所有可能接口
        matches = sheet1[sheet1['物料类别'] == row['设备类型']]
        row_log.append(f"找到{len(matches)}个可能的接口类型")

        # 检查所有匹配行（不再在第一个匹配后break）
        matched_count = 0
        for match_idx, match in matches.iterrows():
            wire_property = match.get('线材属性', '')
            wire_color = row['颜色/线径标识']
            row_log.append(f"检查接口 {match_idx}: 线材属性='{wire_property}'")

            if not evaluate_wire_property(wire_color, 1, wire_property):
                row_log.append(f"条件不满足，跳过")
                continue

            interface = match['接口类型']
            probability = match['概率']
            row_log.append(f"匹配成功: 接口类型={interface}, 概率={probability}")

            # 解析接口类型
            if interface in ['单长', '单短', '双长', '双短']:
                length = interface[1]
                wire_type = interface[0]
            else:
                length = None
                wire_type = '单'

            result.append({
                '设备类型': row['设备类型'],
                '颜色/线径标识': row['颜色/线径标识'],  # 保留线材类型信息
                '对应线径': row['对应线径'],
                '接口类型': interface,
                '解析长度': length,
                '并线要求': wire_type,
                '概率': probability
            })

            matched_count += 1
            # 记录匹配日志（每个匹配都记录）
            match_log = append_match_log(match_log, {
                '匹配类型': '接口类型',
                '匹配对象': f"{row['设备类型']} ({row['颜色/线径标识']})",
                '匹配条件': f"线材属性: {wire_property}",
                '匹配结果': f"接口类型: {interface}",
                '匹配数量': probability,
                '备注': f"实际线材: {wire_color}, 点数:1"
            })

        if matched_count == 0:
            row_log.append("未找到匹配的接口类型")
            match_log = append_match_log(match_log, {
                '匹配类型': '接口类型',
                '匹配对象': f"{row['设备类型']} ({row['颜色/线径标识']})",
                '匹配条件': f"设备类型匹配",
                '匹配结果': '匹配失败',
                '匹配数量': 0,
                '备注': '无满足条件的接口类型'
            })

        logger.debug(f"接口匹配详情记录 {idx}:\n" + "\n".join(row_log))

    logger.info(f"接口类型匹配完成: 共匹配到{len(result)}条记录")
    return pd.DataFrame(result), match_log


def match_terminal_records(df, sheet2):
    """匹配压头记录并统计数量"""
    terminal_counts = {}
    match_log = pd.DataFrame(columns=[
        '匹配类型', '匹配对象', '匹配条件', '匹配结果', '匹配数量', '备注'
    ])

    logger.info(f"开始压头记录匹配: {len(df)}条接口记录")

    if df.empty:
        logger.warning("压头记录匹配数据为空，跳过处理")
        return pd.DataFrame(), match_log

    for idx, row in df.iterrows():
        row_log = []
        row_log.append(
            f"处理记录 {idx}: 设备类型={row['设备类型']}, 接口类型={row.get('接口类型', 'N/A')}, 并线要求={row.get('并线要求', 'N/A')}, 线径={row['对应线径']}")

        # 构建匹配条件
        formatted_diam = format_diameter(row['对应线径'])
        conditions = [
            sheet2['并线要求'] == row['并线要求'],
        ]
        conditions.append(sheet2['对应线径'] == formatted_diam)

        row_log.append(f"基础条件: 并线要求={row['并线要求']}, 线径={row['对应线径']}")

        if '解析长度' in row and row['解析长度']:
            conditions.append(create_interface_type_condition(sheet2, row['解析长度']))
            row_log.append(f"添加接口类型条件: {row['解析长度']}")
        elif '接口类型' in row and row['接口类型']:
            conditions.append(create_interface_type_condition(sheet2, row['接口类型']))
            row_log.append(f"添加接口类型条件: {row['接口类型']}")

        # 将所有条件合并为一个条件，避免索引不匹配警告
        if conditions:
            combined_condition = conditions[0]
            for condition in conditions[1:]:
                combined_condition = combined_condition & condition
            matched = sheet2[combined_condition].copy()
        else:
            matched = sheet2.copy()

        row_log.append(f"匹配条件数量: {len(conditions)}, 匹配结果数量: {len(matched)}")

        # 处理匹配结果
        if not matched.empty:
            terminal = matched.iloc[0]
            key = (terminal['压头星瀚编码'], terminal['压头星空编码'], terminal['压头名称'])

            # 根据线材类型计算压头数量
            wire_type = str(row.get('颜色/线径标识', ''))
            probability = row['概率']

            if wire_type == '四芯线':
                # 四芯线：每根导线需要4个压头
                actual_wire_count = 1 * 4  # 非并线端每次处理1根导线
            elif wire_type == '两芯线':
                # 两芯线：每根导线需要2个压头
                actual_wire_count = 1 * 2  # 非并线端每次处理1根导线
            else:
                # 其他线材：按原有逻辑处理
                actual_wire_count = 1

            # 修改：存储实际数量，不乘以概率
            actual_count = actual_wire_count  # 实际匹配数量

            row_log.append(f"匹配成功: 压头={terminal['压头名称']} ({terminal['压头星瀚编码']}), 实际数量={actual_count}, 概率={probability}")

            # 修改：使用新的数据结构存储
            if key in terminal_counts:
                terminal_counts[key]['实际数量'] += actual_count
            else:
                terminal_counts[key] = {
                    '实际数量': actual_count,
                    '概率': probability,
                    '压头星瀚编码': terminal['压头星瀚编码'],
                    '压头星空编码': terminal['压头星空编码'],
                    '压头名称': terminal['压头名称']
                }

            # 记录匹配日志
            match_log = append_match_log(match_log, {
                '匹配类型': '压头匹配',
                '匹配对象': f"{row['设备类型']}",
                '匹配条件': f"并线要求={row['并线要求']}, 线径={row['对应线径']}, 接口类型={row.get('解析长度', row.get('接口类型', 'N/A'))}",
                '匹配结果': f"压头: {terminal['压头名称']} ({terminal['压头星瀚编码']})",
                '匹配数量': actual_count,
                '备注': f"实际数量: {actual_count}, 概率: {probability}"
            })
        else:
            row_log.append("匹配失败: 未找到符合条件的压头")
            match_log = append_match_log(match_log, {
                '匹配类型': '压头匹配',
                '匹配对象': f"{row['设备类型']}",
                '匹配条件': f"并线要求={row['并线要求']}, 线径={row['对应线径']}, 接口类型={row.get('解析长度', row.get('接口类型', 'N/A'))}",
                '匹配结果': '匹配失败',
                '匹配数量': 0,
                '备注': '未找到压头匹配'
            })

        logger.debug(f"压头匹配详情记录 {idx}:\n" + "\n".join(row_log))

    # 转换为DataFrame
    result = []
    for (h_code, s_code, name), data in terminal_counts.items():
        result.append({
            '屏柜编号': '',  # 屏柜编号将在主函数中设置
            '压头星瀚编码': h_code,
            '压头星空编码': s_code,
            '压头名称': name,
            '实际数量': data['实际数量'],  # 存储实际数量
            '概率': data['概率']          # 存储概率
        })

        # 记录合并日志
        match_log = append_match_log(match_log, {
            '匹配类型': '压头汇总',
            '匹配对象': f"{name} ({h_code})",
            '匹配条件': '合并相同压头',
            '匹配结果': '汇总成功',
            '匹配数量': data['实际数量'],
            '备注': f"实际数量: {data['实际数量']}, 概率: {data['概率']}"
        })

    logger.info(f"压头记录匹配完成: 找到{len(result)}种压头")
    return (pd.DataFrame(result) if result else pd.DataFrame(),
            match_log)


def process_wire_terminal_matching(wire_count_df, sheet1, sheet2):
    """
    处理导线统计表的压头匹配
    参数:
    wire_count_df -- 导线统计结果DataFrame
    sheet1 -- 压头匹配表Sheet1
    sheet2 -- 压头匹配表Sheet2

    返回:
    压头匹配结果DataFrame, 匹配日志DataFrame
    """
    terminal_counts = []
    match_log = pd.DataFrame(columns=[
        '匹配类型', '匹配对象', '匹配条件', '匹配结果', '匹配数量', '备注'
    ])

    logger.info(f"开始处理导线统计表压头匹配: 共{len(wire_count_df)}条导线记录")

    if wire_count_df.empty:
        logger.info("导线统计表为空，跳过处理")
        match_log = append_match_log(match_log, {
            '匹配类型': '导线统计压头',
            '匹配对象': '所有导线',
            '匹配条件': '导线统计表为空',
            '匹配结果': '跳过处理',
            '匹配数量': 0,
            '备注': '导线统计表无数据'
        })
        return pd.DataFrame(), match_log

    logger.info("正在遍历导线统计表并处理起点/终点设备...")

    # 遍历导线统计表的每条记录
    for idx, row in wire_count_df.iterrows():
        # 记录当前处理的导线信息
        logger.info(f"处理导线记录 #{idx + 1}:")
        logger.info(f"  导线标识: {row['颜色/线径标识']}")
        logger.info(f"  线径: {row['对应线径']}")
        logger.info(f"  设备类型: {row['设备类型（起点/终点）']}")
        logger.info(f"  导线根数: {row['导线根数']}")

        # 拆分设备类型为起点和终点
        if '/' in row['设备类型（起点/终点）']:
            start_device, end_device = row['设备类型（起点/终点）'].split('/', 1)
            logger.info(f"  起点设备: {start_device}")
            logger.info(f"  终点设备: {end_device}")
        else:
            start_device = end_device = row['设备类型（起点/终点）']
            logger.info(f"  单一设备: {start_device}")

        # 处理起点设备
        logger.info(f"  处理起点设备: {start_device}")
        start_count_before = len(terminal_counts)
        terminal_counts, match_log = process_device_terminal(
            start_device,
            row,
            sheet1,
            sheet2,
            terminal_counts,
            match_log,
            is_start=True
        )
        start_count_after = len(terminal_counts)
        logger.info(f"  起点设备匹配结果: 新增 {start_count_after - start_count_before} 种压头")

        # 处理终点设备
        logger.info(f"  处理终点设备: {end_device}")
        end_count_before = len(terminal_counts)
        terminal_counts, match_log = process_device_terminal(
            end_device,
            row,
            sheet1,
            sheet2,
            terminal_counts,
            match_log,
            is_start=False
        )
        end_count_after = len(terminal_counts)
        logger.info(f"  终点设备匹配结果: 新增 {end_count_after - end_count_before} 种压头")

        logger.info(f"完成导线记录 #{idx + 1} 处理")

    logger.info(f"完成导线统计表压头匹配: 总共找到 {len(terminal_counts)} 种压头")
    match_log = append_match_log(match_log, {
        '匹配类型': '导线统计压头',
        '匹配对象': '所有导线',
        '匹配条件': '完成处理',
        '匹配结果': '匹配完成',
        '匹配数量': len(terminal_counts),
        '备注': f"总共找到 {len(terminal_counts)} 种压头"
    })

    return (pd.DataFrame(terminal_counts) if terminal_counts else pd.DataFrame(),
            match_log)


def process_device_terminal(device, row, sheet1, sheet2, terminal_counts, match_log, is_start):
    """
    处理单个设备的压头匹配（支持多个接口类型）
    """
    position = "起点" if is_start else "终点"

    # 检查是否为包含U、I的线材
    wire_type = str(row['颜色/线径标识'])
    has_u = 'U' in wire_type
    has_i = 'I' in wire_type

    # 检查是否为线径0.12的导线
    # wire_diameter = row.get('对应线径', 0)
    # is_012_wire = False
    # try:
    #     if float(wire_diameter) == 0.12:
    #         is_012_wire = True
    # except (ValueError, TypeError):
    #     pass

    # 只处理包含U或I的线材，且只显示前三个屏柜
    cabinet_number = row.get('屏柜编号', '')
    is_debug_cabinet = cabinet_number in ['P01', 'P02', 'P03'] or any(cabinet_number.startswith(f'P{i:02d}') for i in range(1, 4))

    # 为线径0.12的导线添加详细调试输出
    # if is_012_wire:
    #     print(f"\n🔍 [0.12线径调试] 处理设备: {device} ({position}) - 屏柜: {cabinet_number}")
    #     print(f"  📏 线径: {wire_diameter} → 格式化后: {format_diameter(wire_diameter)}")
    #     print(f"  🎨 线材标识: {wire_type} (包含U: {has_u}, 包含I: {has_i})")
    #     print(f"  📦 导线根数: {row.get('导线根数', 'N/A')}")

    # if (has_u or has_i) and is_debug_cabinet:
    #     print(f"处理设备: {device} ({position}) - 屏柜: {cabinet_number}")
    #     print(f"  线材标识: {wire_type} (包含U: {has_u}, 包含I: {has_i})")

    # 步骤1: 在Sheet1中匹配所有可能的接口类型
    interface_matches = []

    # 获取该设备的所有可能接口
    for _, sheet1_row in sheet1.iterrows():
        if sheet1_row['物料类别'] != device:
            continue

        wire_property = str(sheet1_row.get('线材属性', ''))

        # 跳过含point的条件（并线专用）
        if 'point' in wire_property:
            continue

        # 检查U/I条件
        if wire_property == '~U&~I' and (has_u or has_i):
            continue
        elif wire_property == 'U/I' and not (has_u or has_i):
            continue
        elif wire_property == '~U' and has_u:
            continue
        elif wire_property == 'U' and not has_u:
            continue
        elif wire_property == '~I' and has_i:
            continue
        elif wire_property == 'I' and not has_i:
            continue

        interface_matches.append(sheet1_row)

    if not interface_matches:
        # if is_012_wire:
        #     print(f"  ❌ 未找到任何匹配的接口类型")
        #     print(f"     设备类型: {device}")
        #     print(f"     线材属性检查失败")
        # if (has_u or has_i) and is_debug_cabinet:
        #     print(f"  未找到任何匹配的接口类型")
        match_log = append_match_log(match_log, {
            '匹配类型': '导线统计压头',
            '匹配对象': f"{device} ({position})",
            '匹配条件': f"设备类型匹配",
            '匹配结果': '接口匹配失败',
            '匹配数量': 0,
            '备注': f'未找到符合条件的接口类型。设备:{device}, 线材:{wire_type}'
        })
        return terminal_counts, match_log

    # if is_012_wire:
    #     print(f"  ✅ 找到 {len(interface_matches)} 个匹配的接口类型")
    # if (has_u or has_i) and is_debug_cabinet:
    #     print(f"  找到 {len(interface_matches)} 个匹配的接口类型")

    # 步骤2: 处理所有匹配到的接口类型，只输出前三个匹配结果
    match_count = 0
    for interface_match in interface_matches:
        try:
            # 获取接口类型和概率
            interface_type = interface_match['接口类型']
            probability = interface_match['概率']

            # if is_012_wire:
            #     print(f"  🔄 处理接口类型 {match_count + 1}: {interface_type} (概率: {probability})")

            # 解析接口类型
            if interface_type in ['单长', '单短', '双长', '双短']:
                wire_type_code = interface_type[0]  # '单'或'双'
                length_type = interface_type[1]  # '长'或'短'
            else:
                wire_type_code = '单'  # 默认
                length_type = None

            # if is_012_wire:
            #     print(f"     解析结果: 并线要求={wire_type_code}, 长度类型={length_type}")

            # 步骤3: 在Sheet2中匹配压头
            try:
                wire_diam = format_diameter(row['对应线径'])  # 确保转为字符串 "2.5"
            except (ValueError, TypeError):
                wire_diam = format_diameter(row['对应线径'])

            # 构建匹配条件
            terminal_conditions = [
                sheet2['并线要求'] == wire_type_code,
                sheet2['对应线径'] == wire_diam
            ]

            # 修正：如果是单长/单短/双长/双短，按原逻辑用length_type，否则用接口类型本身
            if interface_type in ['单长', '单短', '双长', '双短']:
                terminal_conditions.append(create_interface_type_condition(sheet2, length_type))
            else:
                terminal_conditions.append(create_interface_type_condition(sheet2, interface_type))

            # 将所有条件合并为一个条件，避免索引不匹配警告
            if terminal_conditions:
                combined_condition = terminal_conditions[0]
                for condition in terminal_conditions[1:]:
                    combined_condition = combined_condition & condition
                matched_terminals = sheet2[combined_condition].copy()
            else:
                matched_terminals = sheet2.copy()

            # 步骤4: 计算压头数量（每个接口类型单独计算）
            if not matched_terminals.empty:
                terminal = matched_terminals.iloc[0]

                # 根据线材类型计算压头数量
                wire_type = str(row['颜色/线径标识'])
                base_count = row['导线根数']

                if wire_type == '四芯线':
                    # 四芯线：每根导线需要4个压头
                    actual_wire_count = base_count * 4
                elif wire_type == '两芯线':
                    # 两芯线：每根导线需要2个压头
                    actual_wire_count = base_count * 2
                else:
                    # 其他线材：按原有逻辑处理
                    actual_wire_count = base_count

                # 修改：存储实际数量，不乘以概率
                actual_quantity = actual_wire_count  # 实际匹配数量

                # if is_012_wire:
                #     print(f"     ✅ 匹配成功!")
                #     print(f"        压头名称: {terminal['压头名称']}")
                #     print(f"        星瀚编码: {terminal['压头星瀚编码']}")
                #     print(f"        星空编码: {terminal['压头星空编码']}")
                #     if wire_type == '四芯线':
                #         print(f"        数量计算: {base_count} 根四芯线 × 4 × {probability} 概率 = {base_count} × 4 × {probability} = {quantity}")
                #     elif wire_type == '两芯线':
                #         print(f"        数量计算: {base_count} 根两芯线 × 2 × {probability} 概率 = {base_count} × 2 × {probability} = {quantity}")
                #     else:
                #         print(f"        数量计算: {base_count} 根 × {probability} 概率 = {quantity}")
                #     print(f"        损耗率: {loss_rate}")

                # 只输出前三个匹配结果，且只显示包含U、I的线材的前三款屏柜
                # if match_count < 3 and (has_u or has_i) and is_debug_cabinet:
                #     print(f"  匹配过程 {match_count + 1}:")
                #     print(f"    接口类型: {interface_type}, 概率: {probability}")
                #     print(f"    并线要求: {wire_type_code}, 线径: {wire_diam}")
                #     if length_type:
                #         print(f"    长度要求: {length_type}")
                #     print(f"    匹配结果: {terminal['压头名称']} (星瀚编码: {terminal['压头星瀚编码']}, 数量: {quantity})")
                #     print(f"    计算过程: {row['导线根数']} 根导线 × {probability} 概率 = {quantity}")
                #     match_count += 1

                terminal_counts.append({
                    '屏柜编号': row.get('屏柜编号', ''),  # 获取屏柜编号
                    '压头星瀚编码': terminal['压头星瀚编码'],
                    '压头星空编码': terminal['压头星空编码'],
                    '压头名称': terminal['压头名称'],
                    '实际数量': actual_quantity,  # 存储实际数量
                    '概率': probability           # 存储概率
                })

                # 记录日志
                match_log = append_match_log(match_log, {
                    '匹配类型': '导线统计压头',
                    '匹配对象': f"{device} ({position})",
                    '匹配条件': f"线径={wire_diam}, 接口={interface_type}",
                    '匹配结果': f"匹配成功: {terminal['压头名称']}",
                    '匹配数量': actual_quantity,
                    '备注': f"实际数量: {actual_quantity}, 概率: {probability} (屏柜: {row.get('屏柜编号', '')})"
                })
            else:
                # if is_012_wire:
                #     print(f"     ❌ 匹配失败!")
                #     print(f"        查找条件: 并线要求={wire_type_code}, 线径={wire_diam}")
                #     if length_type:
                #         print(f"        接口类型: {length_type}")
                #     print(f"        失败原因: 未找到符合条件的压头")

                # 只输出前三个失败结果，且只显示包含U、I的线材的前三款屏柜
                # if match_count < 3 and (has_u or has_i) and is_debug_cabinet:
                #     print(f"  匹配失败 {match_count + 1}:")
                #     print(f"    接口类型: {interface_type}, 概率: {probability}")
                #     print(f"    并线要求: {wire_type_code}, 线径: {wire_diam}")
                #     if length_type:
                #         print(f"    长度要求: {length_type}")
                #     print(f"    失败原因: 未找到匹配压头")
                #     match_count += 1
                
                match_log = append_match_log(match_log, {
                    '匹配类型': '导线统计压头',
                    '匹配对象': f"{device} ({position})",
                    '匹配条件': f"线径={wire_diam}, 接口={interface_type}",
                    '匹配结果': '压头匹配失败',
                    '匹配数量': 0,
                    '备注': f"未找到匹配的压头: 并线要求={wire_type_code}, 线径={wire_diam}" +
                            (f", 接口类型={length_type}" if length_type else "")
                })

        except Exception as e:
            logger.error(f"        处理接口匹配时出错: {str(e)}")
            match_log = append_match_log(match_log, {
                '匹配类型': '导线统计压头',
                '匹配对象': f"{device} ({position})",
                '匹配条件': '处理接口匹配',
                '匹配结果': '出错',
                '匹配数量': 0,
                '备注': f"错误信息: {str(e)}"
            })

    return terminal_counts, match_log


def process_printer_terminals(cabinet, printer_grouped_df, pp_terminals, sheet1=None, sheet2=None, terminal_file=None):
    """
    处理数据线（打印机）压头匹配
    参数:
        cabinet: 当前屏柜编号
        printer_grouped_df: 分组后的打印机数据线DataFrame
        pp_terminals: Sheet2中所有接口类型为PP的压头
        sheet1: 压头匹配表Sheet1（用于匹配另一端压头）
        sheet2: 压头匹配表Sheet2（用于匹配另一端压头）

    返回:
        压头结果DataFrame, 匹配日志DataFrame
    """
    terminal_counts = []
    match_log = pd.DataFrame(columns=[
        '匹配类型', '匹配对象', '匹配条件', '匹配结果', '匹配数量', '备注'
    ])

    # 检查是否有打印机数据线
    if printer_grouped_df is None or printer_grouped_df.empty:
        logger.info(f"屏柜{cabinet}无打印机数据线，跳过处理")
        return pd.DataFrame(), match_log, []

    # 获取当前屏柜的数据线分组
    cabinet_groups = printer_grouped_df[printer_grouped_df['屏柜编号'] == cabinet]
    if cabinet_groups.empty:
        logger.info(f"屏柜{cabinet}无打印机数据线分组")
        return pd.DataFrame(), match_log, []

    # 判断是否为调试屏柜（前3个屏柜）
    is_debug_cabinet = cabinet in ['P01', 'P02', 'P03'] or any(cabinet.startswith(f'P{i:02d}') for i in range(1, 4))
    
    # 移除调试输出，只保留压头匹配结果

    # 遍历每组数据线
    for _, group_row in cabinet_groups.iterrows():
        group_id = group_row.get('设备标号', '未知组号')
        device_type = group_row.get('设备类型（起点/终点）', '')
        wire_diameter = group_row.get('对应线径', '')

        # 1. 为PP端匹配压头
        for _, terminal_row in pp_terminals.iterrows():
            # 获取损耗率
            loss_rate = terminal_row.get('损耗率', 0)
            if pd.isna(loss_rate):
                loss_rate = 0

            # 确保损耗率是数值类型
            try:
                loss_rate = float(loss_rate)
            except (ValueError, TypeError):
                loss_rate = 0.0

            terminal_counts.append({
                '屏柜编号': cabinet,  # 添加屏柜编号
                '压头星瀚编码': terminal_row['压头星瀚编码'],
                '压头星空编码': terminal_row['压头星空编码'],
                '压头名称': terminal_row['压头名称'],
                '数量': 1,  # 每组数据线配一套
                '实际数量': 1,  # 添加实际数量列
                '概率': 1.0,  # 添加概率列，PP压头概率为1.0
                '损耗率': loss_rate  # 添加损耗率信息
            })

            # 记录日志
            match_log = append_match_log(match_log, {
                '匹配类型': '数据线压头(PP端)',
                '匹配对象': f"数据线组({group_id})",
                '匹配条件': '接口类型=PP',
                '匹配结果': f"匹配成功: {terminal_row['压头名称']}",
                '匹配数量': 1,
                '备注': f"屏柜:{cabinet}, 组号:{group_id}, PP端, 损耗率: {loss_rate}"
            })

        # 1.1. 为每套PP压头添加1份焊锡丝
        # 从Sheet3获取焊锡丝信息
        try:
            # 读取Sheet3（焊锡丝配置）
            if terminal_file:
                sheet3 = pd.read_excel(terminal_file, sheet_name=2)
            else:
                # 如果没有传入文件路径，使用默认路径
                sheet3 = pd.read_excel('input/配置文件/压头匹配.xlsx', sheet_name=2)
            solder_records = sheet3[sheet3['接口类型'] == 'PP']

            if not solder_records.empty:
                solder_row = solder_records.iloc[0]  # 取第一条焊锡丝记录

                # 获取焊锡丝的损耗率和长度
                solder_loss_rate = solder_row.get('损耗率', 0)
                solder_length = solder_row.get('长度', 0.0017)  # 默认0.0017

                # 确保数值类型
                try:
                    solder_loss_rate = float(solder_loss_rate)
                    solder_length = float(solder_length)
                except (ValueError, TypeError):
                    solder_loss_rate = 0.0
                    solder_length = 0.0017

                # 计算焊锡丝的残值（类似套管处理）
                from decimal_calculator import calculate_wire_residual

                # 获取最小有效值小数位数
                min_valid_value = solder_row.get('最小有效值', 0.000001)  # 默认最小有效值
                min_valid_decimals = 6  # 根据最小有效值0.000001确定小数位数

                # 计算截断值和残值
                truncated_length, solder_residual = calculate_wire_residual(
                    solder_length, solder_loss_rate, min_valid_decimals
                )

                # 添加焊锡丝记录
                terminal_counts.append({
                    '屏柜编号': cabinet,
                    '压头星瀚编码': solder_row['物料星瀚编码'],
                    '压头星空编码': solder_row.get('物料星空编码', '/'),
                    '压头名称': solder_row['物料名称'],
                    '数量': truncated_length,  # 使用截断后的长度
                    '实际数量': truncated_length,
                    '概率': 1.0,
                    '损耗率': solder_loss_rate,
                    '残值': solder_residual,  # 添加残值信息
                    '最小有效值': min_valid_value,
                    '最小有效值小数位数': min_valid_decimals
                })

                # 记录日志
                match_log = append_match_log(match_log, {
                    '匹配类型': '数据线焊锡丝',
                    '匹配对象': f"数据线组({group_id})",
                    '匹配条件': '每套PP压头配1份焊锡丝',
                    '匹配结果': f"匹配成功: {solder_row['物料名称']}",
                    '匹配数量': solder_length,
                    '备注': f"屏柜:{cabinet}, 组号:{group_id}, 焊锡丝长度: {solder_length}, 损耗率: {solder_loss_rate}"
                })

        except Exception as e:
            logger.warning(f"添加焊锡丝失败: {e}")
            # 如果读取Sheet3失败，使用默认值
            from decimal_calculator import calculate_wire_residual

            # 默认焊锡丝参数
            default_length = 0.0017
            default_loss_rate = 0.0
            default_min_valid_decimals = 6  # 对应最小有效值0.000001

            # 计算默认焊锡丝的残值
            truncated_default_length, default_residual = calculate_wire_residual(
                default_length, default_loss_rate, default_min_valid_decimals
            )

            terminal_counts.append({
                '屏柜编号': cabinet,
                '压头星瀚编码': 60204000044,  # 默认焊锡丝编码
                '压头星空编码': '/',
                '压头名称': '焊锡丝',
                '数量': truncated_default_length,  # 使用截断后的长度
                '实际数量': truncated_default_length,
                '概率': 1.0,
                '损耗率': 0.0,
                '残值': default_residual,  # 添加残值信息
                '最小有效值': 0.000001,
                '最小有效值小数位数': default_min_valid_decimals
            })

            match_log = append_match_log(match_log, {
                '匹配类型': '数据线焊锡丝',
                '匹配对象': f"数据线组({group_id})",
                '匹配条件': '每套PP压头配1份焊锡丝(默认)',
                '匹配结果': '匹配成功: 焊锡丝(默认)',
                '匹配数量': 0.0017,
                '备注': f"屏柜:{cabinet}, 组号:{group_id}, 使用默认焊锡丝配置"
            })

        # 2. 为另一端匹配压头（线材属性为~U&~I）
        if sheet1 is not None and sheet2 is not None and device_type and wire_diameter:
            # 分割设备类型获取另一端设备
            device_parts = str(device_type).split('/')
            if len(device_parts) >= 2:
                # 确定哪一端是PP端，哪一端是另一端
                start_point = group_row.get('导线起点', '')
                end_point = group_row.get('导线终点', '')
                
                # 判断PP端
                if 'PP' in str(start_point):
                    # PP端在起点，另一端在终点
                    other_device = device_parts[1] if len(device_parts) > 1 else device_parts[0]
                elif 'PP' in str(end_point):
                    # PP端在终点，另一端在起点
                    other_device = device_parts[0]
                else:
                    # 无法确定，跳过
                    logger.warning(f"无法确定数据线组({group_id})的PP端位置")
                    continue

                logger.info(f"数据线组({group_id})的另一端设备: {other_device}")

                # 在Sheet1中查找另一端设备的接口类型（线材属性为~U&~I）
                device_matches = sheet1[sheet1['物料类别'] == other_device]
                valid_matches = device_matches[
                    (device_matches['线材属性'] == '~U&~I') |
                    (device_matches['线材属性'].isna()) |
                    (device_matches['线材属性'] == '')
                ]

                if not valid_matches.empty:
                    # 处理每个匹配的接口类型
                    for _, match in valid_matches.iterrows():
                        interface_type = match['接口类型']
                        probability = match['概率']

                        # 解析接口类型
                        if interface_type in ['单长', '单短', '双长', '双短']:
                            wire_type_code = interface_type[0]  # 单/双
                            length_type = interface_type[1]  # 长/短
                        else:
                            wire_type_code = interface_type
                            length_type = None

                        # 在Sheet2中匹配压头
                        # 使用format_diameter函数统一线径格式
                        formatted_wire_diameter = format_diameter(wire_diameter)
                        terminal_conditions = [
                            sheet2['并线要求'] == str(wire_type_code),
                            sheet2['对应线径'] == formatted_wire_diameter
                        ]

                        if length_type:
                            terminal_conditions.append(create_interface_type_condition(sheet2, length_type))

                        # 将所有条件合并为一个条件，避免索引不匹配警告
                        if terminal_conditions:
                            # 使用 & 操作符合并所有布尔条件
                            combined_condition = terminal_conditions[0]
                            for condition in terminal_conditions[1:]:
                                combined_condition = combined_condition & condition
                            matched_terminals = sheet2[combined_condition].copy()
                        else:
                            matched_terminals = sheet2.copy()

                        if not matched_terminals.empty:
                            terminal = matched_terminals.iloc[0]
                            # 调试输出根数
                            logger.debug(f"数据线组({group_id})根数字段内容: {group_row.get('根数', '无')}")
                            # print(f"数据线组({group_id})根数字段内容: {group_row.get('根数', '无')}")
                            # 计算最终数量：只取根数，不乘以概率，然后向上取整
                            final_quantity = math.ceil(group_row.get('根数', 1))
                            
                            # 获取损耗率
                            loss_rate = terminal.get('损耗率', 0)
                            if pd.isna(loss_rate):
                                loss_rate = 0
                            
                            # 确保损耗率是数值类型
                            try:
                                loss_rate = float(loss_rate)
                            except (ValueError, TypeError):
                                loss_rate = 0.0
                                
                            terminal_counts.append({
                                '屏柜编号': cabinet,  # 添加屏柜编号
                                '压头星瀚编码': terminal['压头星瀚编码'],
                                '压头星空编码': terminal['压头星空编码'],
                                '压头名称': terminal['压头名称'],
                                '实际数量': group_row.get('根数', 1),  # 存储实际根数
                                '概率': 1.0  # 数据线压头概率为1
                            })

                            # 记录日志
                            match_log = append_match_log(match_log, {
                                '匹配类型': '数据线压头(另一端)',
                                '匹配对象': f"数据线组({group_id})",
                                '匹配条件': f"设备类型={other_device}, 接口类型={interface_type}, 线径={wire_diameter}",
                                '匹配结果': f"匹配成功: {terminal['压头名称']}",
                                '匹配数量': group_row.get('根数', 1),
                                '备注': f"屏柜:{cabinet}, 组号:{group_id}, 另一端设备:{other_device}, 根数:{group_row.get('根数', 1)}"
                            })

                            logger.info(f"数据线组({group_id})另一端压头匹配成功: {terminal['压头名称']}")
                        else:
                            # 注释掉详细调试输出
                            # logger.warning(
                            #     f"数据线组({group_id})另一端压头匹配失败: 设备类型={other_device}, 接口类型={interface_type}, 线径={wire_diameter}"
                            # )
                            # # 1. 打印Sheet2所有可用的并线要求、线径、接口类型
                            # available_wire_types = sheet2['并线要求'].unique()
                            # available_diameters = sheet2['对应线径'].unique()
                            # available_interfaces = sheet2['接口类型'].unique()
                            # logger.warning(f"Sheet2可用并线要求: {list(available_wire_types)}")
                            # logger.warning(f"Sheet2可用线径: {list(available_diameters)}")
                            # logger.warning(f"Sheet2可用接口类型: {list(available_interfaces)}")
                            # # 2. 分步分析
                            # wire_type_matches = sheet2[sheet2['并线要求'] == str(wire_type_code)]
                            # if wire_type_matches.empty:
                            #     logger.warning(f"第1步失败: 没有找到并线要求='{wire_type_code}'的记录")
                            # else:
                            #     logger.warning(f"第1步成功: 找到 {len(wire_type_matches)} 条并线要求='{wire_type_code}'的记录")
                            #     diameter_matches = wire_type_matches[wire_type_matches['对应线径'] == formatted_wire_diameter]
                            #     if diameter_matches.empty:
                            #         logger.warning(f"第2步失败: 在并线要求匹配的记录中，没有找到线径='{formatted_wire_diameter}'")
                            #         available_diams_for_type = wire_type_matches['对应线径'].unique()
                            #         logger.warning(f"可用线径: {list(available_diams_for_type)}")
                            #     else:
                            #         logger.warning(f"第2步成功: 找到 {len(diameter_matches)} 条线径='{formatted_wire_diameter}'的记录")
                            #         if length_type:
                            #             interface_matches = diameter_matches[diameter_matches['接口类型'] == length_type]
                            #             if interface_matches.empty:
                            #                 logger.warning(f"第3步失败: 在前两步匹配的记录中，没有找到接口类型='{length_type}'")
                            #                 available_interfaces_for_diam = diameter_matches['接口类型'].unique()
                            #                 logger.warning(f"可用接口类型: {list(available_interfaces_for_diam)}")
                            #             else:
                            #                 logger.warning(f"第3步成功: 找到接口类型='{length_type}'的记录")
                            #         else:
                            #             logger.warning(f"无接口类型要求但仍匹配失败: 逻辑可能有问题")
                            match_log = append_match_log(match_log, {
                                '匹配类型': '数据线压头(另一端)',
                                '匹配对象': f"数据线组({group_id})",
                                '匹配条件': f"设备类型={other_device}, 接口类型={interface_type}, 线径={wire_diameter}",
                                '匹配结果': '压头匹配失败',
                                '匹配数量': 0,
                                '备注': f"屏柜:{cabinet}, 组号:{group_id}, 另一端设备:{other_device}"
                            })
                else:
                    logger.warning(f"数据线组({group_id})的另一端设备({other_device})未找到匹配的接口类型")
                    match_log = append_match_log(match_log, {
                        '匹配类型': '数据线压头(另一端)',
                        '匹配对象': f"数据线组({group_id})",
                        '匹配条件': f"设备类型={other_device}, 线材属性=~U&~I",
                        '匹配结果': '接口类型匹配失败',
                        '匹配数量': 0,
                        '备注': f"屏柜:{cabinet}, 组号:{group_id}, 另一端设备:{other_device}"
                    })
            else:
                logger.warning(f"数据线组({group_id})的设备类型格式不正确: {device_type}")
        else:
            logger.warning(f"数据线组({group_id})缺少必要参数，跳过另一端压头匹配")

    # 收集焊锡丝残值信息
    solder_residual_list = []
    terminal_df = pd.DataFrame(terminal_counts)

    if not terminal_df.empty:
        # 查找焊锡丝记录
        solder_records = terminal_df[terminal_df['压头名称'].str.contains('焊锡', na=False)]
        for idx, row in solder_records.iterrows():
            residual_value = row.get('残值', 0)
            if abs(residual_value) > 1e-15:  # 使用与套管相同的阈值
                solder_residual_list.append({
                    '物料编码': row['压头星瀚编码'],
                    '物料名称': row['压头名称'],
                    '残值': residual_value
                })

    logger.info(f"数据线压头匹配完成: 共匹配{len(terminal_counts)}个压头记录，焊锡丝残值{len(solder_residual_list)}项")
    return terminal_df, match_log, solder_residual_list


def process_power_wire_terminals(power_wire_df, sheet1, sheet2):
    """处理电源线压头匹配"""
    terminal_counts = []
    match_log = pd.DataFrame(columns=[
        '匹配类型', '匹配对象', '匹配条件', '匹配结果', '匹配数量', '备注'
    ])

    # 移除调试输出，只保留压头匹配结果
    if power_wire_df.empty:
        logger.info("电源线记录表为空，跳过处理")
        match_log = append_match_log(match_log, {
            '匹配类型': '电源线压头',
            '匹配对象': '所有电源线',
            '匹配条件': '电源线记录表为空',
            '匹配结果': '跳过处理',
            '匹配数量': 0,
            '备注': '无电源线数据'
        })
        return pd.DataFrame(), match_log

    for idx, row in power_wire_df.iterrows():
        # 获取设备类型并分割
        device_types = str(row['设备类型（起点/终点）']).split('/')
        device_type = device_types[0] if device_types[0] != '电源线' else device_types[1]

        # 在Sheet1中匹配物料类别
        matches = sheet1[sheet1['物料类别'] == device_type]

        # 过滤线材属性为~U&~I或空值的记录
        valid_matches = matches[
            (matches['线材属性'].isna()) |
            (matches['线材属性'] == '~U&~I') |
            (matches['线材属性'] == '')
            ]

        if valid_matches.empty:
            logger.warning(f"未找到匹配的物料类别: {device_type}")
            match_log = append_match_log(match_log, {
                '匹配类型': '电源线压头',
                '匹配对象': device_type,
                '匹配条件': f"物料类别匹配",
                '匹配结果': '匹配失败',
                '匹配数量': 0,
                '备注': f"未找到物料类别: {device_type} 且线材属性为~U&~I或空值的记录"
            })
            continue

        # 处理每个匹配的接口类型
        for _, match in valid_matches.iterrows():
            interface_type = match['接口类型']
            probability = match['概率']

            # 解析接口类型
            if interface_type in ['单长', '单短', '双长', '双短']:
                wire_type = interface_type[0]  # 单/双
                length_type = interface_type[1]  # 长/短
            else:
                wire_type = interface_type
                length_type = None

            # 在Sheet2中匹配压头
            terminal_conditions = [
                sheet2['并线要求'] == wire_type,
                sheet2['对应线径'] == '1.0'
            ]

            if length_type:
                terminal_conditions.append(create_interface_type_condition(sheet2, length_type))

            # 将所有条件合并为一个条件，避免索引不匹配警告
            if terminal_conditions:
                combined_condition = terminal_conditions[0]
                for condition in terminal_conditions[1:]:
                    combined_condition = combined_condition & condition
                matched_terminals = sheet2[combined_condition].copy()
            else:
                matched_terminals = sheet2.copy()

            if matched_terminals.empty:
                match_log = append_match_log(match_log, {
                    '匹配类型': '电源线压头',
                    '匹配对象': device_type,
                    '匹配条件': f"接口类型={interface_type}",
                    '匹配结果': '压头匹配失败',
                    '匹配数量': 0,
                    '备注': f"未找到匹配压头: 并线要求={wire_type}, 线径=1.0" +
                            (f", 接口类型={length_type}" if length_type else "")
                })
                continue

            terminal = matched_terminals.iloc[0]
            
            # 获取损耗率
            loss_rate = terminal.get('损耗率', 0)
            if pd.isna(loss_rate):
                loss_rate = 0
            
            # 确保损耗率是数值类型
            try:
                loss_rate = float(loss_rate)
            except (ValueError, TypeError):
                loss_rate = 0.0
            
            terminal_counts.append({
                '屏柜编号': row.get('屏柜编号', ''),  # 添加屏柜编号
                '压头星瀚编码': terminal['压头星瀚编码'],
                '压头星空编码': terminal['压头星空编码'],
                '压头名称': terminal['压头名称'],
                '实际数量': 1,  # 电源线实际数量为1
                '概率': probability  # 存储概率
            })
            match_log = append_match_log(match_log, {
                '匹配类型': '电源线压头',
                '匹配对象': device_type,
                '匹配条件': f"接口类型={interface_type}",
                '匹配结果': f"匹配成功: {terminal['压头名称']}",
                '匹配数量': probability,
                '备注': f"概率值: {probability}, 损耗率: {loss_rate}"
            })

    return (pd.DataFrame(terminal_counts) if terminal_counts else pd.DataFrame(),
            match_log)


def process_multi_core_terminals(multi_core_df, sheet1, sheet2):
    """
    处理多芯线压头匹配

    参数:
    multi_core_df -- 多芯线统计DataFrame
    sheet1 -- 压头匹配表Sheet1
    sheet2 -- 压头匹配表Sheet2

    返回:
    压头匹配结果DataFrame, 匹配日志DataFrame
    """
    terminal_counts = []
    match_log = pd.DataFrame(columns=[
        '匹配类型', '匹配对象', '匹配条件', '匹配结果', '匹配数量', '备注'
    ])

    logger.info(f"开始处理多芯线压头匹配: 共{len(multi_core_df)}条多芯线记录")

    if multi_core_df.empty:
        logger.info("多芯线数据为空，跳过处理")
        return pd.DataFrame(), match_log

    # 遍历每条多芯线记录
    for idx, row in multi_core_df.iterrows():
        device_type_full = row['设备类型']
        wire_type = row['颜色/线径标识']  # '四芯线' 或 '两芯线'
        wire_diameter = row['对应线径']  # 0.12 或 0.3

        # 拆分设备类型为起点和终点（类似普通导线的处理方式）
        if '/' in device_type_full:
            start_device, end_device = device_type_full.split('/', 1)
        else:
            start_device = end_device = device_type_full

        # 处理起点和终点设备
        for device_type, position in [(start_device, '起点'), (end_device, '终点')]:
            # 在Sheet1中查找匹配的接口类型
            # 多芯线的线材属性应该是~U&~I和空这两种
            interface_matches = []
            for _, sheet1_row in sheet1.iterrows():
                if sheet1_row['物料类别'] != device_type:
                    continue

                # 检查线材属性条件
                wire_property = str(sheet1_row.get('线材属性', ''))

                # 多芯线只匹配以下两种情况：
                # 1. 线材属性为空或NaN
                # 2. 线材属性为~U&~I
                if pd.isna(sheet1_row.get('线材属性')) or wire_property.strip() == '':
                    # 线材属性为空，符合条件
                    interface_matches.append(sheet1_row)
                elif wire_property.strip() == '~U&~I':
                    # 线材属性为~U&~I，符合条件
                    interface_matches.append(sheet1_row)
                # 其他线材属性条件（如U/I、point相关）都不匹配多芯线

            if not interface_matches:
                match_log = append_match_log(match_log, {
                    '匹配类型': '多芯线压头',
                    '匹配对象': f"{device_type} ({wire_type}) - {position}",
                    '匹配条件': f"设备类型匹配",
                    '匹配结果': '接口匹配失败',
                    '匹配数量': 0,
                    '备注': f'未找到符合条件的接口类型。设备:{device_type}, 线材:{wire_type}, 位置:{position}'
                })
                continue

            # 处理所有匹配到的接口类型
            for interface_match in interface_matches:
                interface_type = interface_match['接口类型']
                probability = interface_match['概率']

                # 解析接口类型
                if interface_type in ['单长', '单短', '双长', '双短']:
                    wire_type_code = interface_type[0]  # '单'或'双'
                    length_type = interface_type[1]  # '长'或'短'
                else:
                    wire_type_code = '单'  # 默认
                    length_type = None

                # 在Sheet2中匹配压头
                # 使用format_diameter函数确保匹配一致性
                formatted_diam = format_diameter(wire_diameter)
                terminal_conditions = [
                    sheet2['并线要求'] == wire_type_code,
                    sheet2['对应线径'].apply(format_diameter) == formatted_diam
                ]

                if length_type:
                    terminal_conditions.append(create_interface_type_condition(sheet2, length_type))

                # 合并条件
                if terminal_conditions:
                    combined_condition = terminal_conditions[0]
                    for condition in terminal_conditions[1:]:
                        combined_condition = combined_condition & condition
                    matched_terminals = sheet2[combined_condition].copy()
                else:
                    matched_terminals = sheet2.copy()

                if not matched_terminals.empty:
                    terminal = matched_terminals.iloc[0]

                    # 计算压头数量（每端的压头数量）
                    if wire_type == '四芯线':
                        # 四芯线：每端需要4个压头
                        actual_wire_count = 1 * 4
                    elif wire_type == '两芯线':
                        # 两芯线：每端需要2个压头
                        actual_wire_count = 1 * 2
                    else:
                        actual_wire_count = 1

                    # 修改：存储实际数量，不乘以概率
                    actual_quantity = actual_wire_count  # 实际匹配数量

                    terminal_counts.append({
                        '屏柜编号': row.get('屏柜编号', ''),  # 添加屏柜编号
                        '压头星瀚编码': terminal['压头星瀚编码'],
                        '压头星空编码': terminal['压头星空编码'],
                        '压头名称': terminal['压头名称'],
                        '实际数量': actual_quantity,  # 存储实际数量
                        '概率': probability           # 存储概率
                    })

                    # 记录匹配日志
                    match_log = append_match_log(match_log, {
                        '匹配类型': '多芯线压头',
                        '匹配对象': f"{device_type} ({wire_type}) - {position}",
                        '匹配条件': f"线径={formatted_diam}, 接口={interface_type}",
                        '匹配结果': f"匹配成功: {terminal['压头名称']}",
                        '匹配数量': actual_quantity,
                        '备注': f"多芯线{position}端实际数量: {actual_quantity}, 概率: {probability} (屏柜: {row.get('屏柜编号', '')})"
                    })
                else:
                    match_log = append_match_log(match_log, {
                        '匹配类型': '多芯线压头',
                        '匹配对象': f"{device_type} ({wire_type}) - {position}",
                        '匹配条件': f"线径={formatted_diam}, 接口={interface_type}",
                        '匹配结果': '压头匹配失败',
                        '匹配数量': 0,
                        '备注': f"未找到匹配的压头: 并线要求={wire_type_code}, 线径={formatted_diam}" +
                                (f", 接口类型={length_type}" if length_type else "")
                    })

    logger.info(f"多芯线压头匹配完成: 找到{len(terminal_counts)}种压头")
    return (pd.DataFrame(terminal_counts) if terminal_counts else pd.DataFrame(),
            match_log)